// Staging Configuration
// This file is used for staging environment testing
// Copy this to config.js when deploying to staging

// Developer Configuration - Update these values per environment
const DEVELOPER_CONFIG = {
  HOSTNAME: '', // Set this for each machine
  PROTOCOL: 'https:', // 'http:' or 'https:'
  FORCE_HTTP: false, // Override to force HTTP
  DEV_MODE: false,
  BYPASS_OAUTH: false,
};

// Service Port Configuration
const SERVICE_PORTS = {
  API: 64037,
  OAUTH: 64023,
  STORAGE: 64038,
  WEB: 8443,
};

/**
 * Get protocol (http/https) based on developer configuration
 */
function getProtocol() {
  // Force HTTP if specified
  if (DEVELOPER_CONFIG.FORCE_HTTP) {
    return 'http:';
  }

  // Use configured protocol
  return DEVELOPER_CONFIG.PROTOCOL;
}

/**
 * Build dynamic URLs based on hostname and protocol
 */
function buildUrls() {
  // Use current machine hostname if HOSTNAME is blank
  const hostname =
    DEVELOPER_CONFIG.HOSTNAME ||
    (typeof window !== 'undefined' && window.location ? window.location.hostname : 'localhost');
  const protocol = getProtocol();

  return {
    API_URL: `${protocol}//${hostname}:${SERVICE_PORTS.API}/api/`,
    OAUTH_URL: `${protocol}//${hostname}:${SERVICE_PORTS.OAUTH}/`,
    STORAGE_URL: `${protocol}//${hostname}:${SERVICE_PORTS.STORAGE}/api/Files`,
    OAUTH_REDIRECT_URI: `${protocol}//${hostname}:${SERVICE_PORTS.WEB}/signin-callback`,
  };
}

// Generate dynamic URLs
const dynamicUrls = buildUrls();

window.APP_CONFIG = {
  // Core API Configuration - Dynamic URLs
  API_URL: dynamicUrls.API_URL,
  OAUTH_URL: dynamicUrls.OAUTH_URL,
  STORAGE_URL: dynamicUrls.STORAGE_URL,
  API_TIMEOUT: 120000, // 2 minutes

  // Environment Configuration
  ENVIRONMENT: 'staging',

  // Staging Configuration
  HOSTNAME: DEVELOPER_CONFIG.HOSTNAME,
  PORT: SERVICE_PORTS.WEB,

  // Cache Configuration
  CACHE_ENABLED: false, // Enable caching in staging
  CACHE_SIZE_MB: 50,
  CACHE_MAX_AGE: 3600000,

  // Development/Debug Configuration
  DEBUG_MODE: false,
  FORCE_HTTP: false,
  ALLOW_HTTP: false, // Enforce HTTPS in staging

  // Logging Configuration
  LOG_LEVEL: 'WARN', // Moderate logging in staging

  // Application Metadata
  APP_VERSION: '105.0.0',

  // OAuth Client Configuration
  OAUTH_CLIENT_ID: 'octoplant-web-client',
  OAUTH_REDIRECT_URI: dynamicUrls.OAUTH_REDIRECT_URI,

  // Additional runtime settings
  THEME: 'default',
  LANGUAGE: 'en',
};

// Staging-specific logging
if (window.console && window.console.log) {
  console.log('[Config] 🚀 Staging configuration loaded:', window.APP_CONFIG);
  console.log('[Config] 🔧 Staging configuration loaded:', {
    configuredHostname: DEVELOPER_CONFIG.HOSTNAME,
    actualHostname:
      DEVELOPER_CONFIG.HOSTNAME ||
      (typeof window !== 'undefined' && window.location ? window.location.hostname : 'localhost'),
  });
}
