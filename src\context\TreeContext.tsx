import React, {
  createContext,
  FC,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { ComponentDetails, TreeNodeType } from '@/utils/types';
import { createTree, fetchTreeNodes } from '@/services/api/index';
import { VersionDetailsCurrentPage } from '@/components/VersionDetails/types';
import { useQueryClient, useMutation } from '@tanstack/react-query';
import { useAuth } from './AuthContext';
import { useComponentTreeBranch } from '@/hooks/queries/useComponentTreeBranch';
import { logDebug, logError } from '@/utils/logger';

interface TreeContextProps {
  data: TreeNodeType[];
  loading: boolean;
  treeLoading: boolean;
  contentLoading: boolean;
  error: string | null;
  selectedDetails: ComponentDetails | null;
  setSelectedDetails: (details: ComponentDetails | null) => void;
  selectedPath: string[];
  setSelectedPath: (path: string[]) => void;
  selectedNode: TreeNodeType | null;
  setSelectedNode: (node: TreeNodeType | null) => void;
  isTreeVisible: boolean;
  toggleTreeVisibility: () => void;
  handleComponentSelect: (details: ComponentDetails | null, path: string[]) => void;
  currentDetailsPage: VersionDetailsCurrentPage;
  setCurrentDetailsPage: (currentPage: VersionDetailsCurrentPage) => void;
  updateTreeWithDirectory: (directoryId: string, directoryInfo?: DirectoryInfo) => Promise<void>;
  // Node expansion state management
  isNodeExpanded: (nodeId: string) => boolean;
  toggleNodeExpansion: (nodeId: string) => void;
  expandNode: (nodeId: string) => void;
  collapseNode: (nodeId: string) => void;
  // Prefetching configuration
  prefetchEnabled: boolean;
  prefetchDelay: number;
}

// Type for directory info to improve type safety
interface DirectoryInfo {
  id: string;
  name: string;
  type: string;
  path: string[];
  description?: string;
  componentType?: any | null;
}

// Type for mutation variables
interface UpdateTreeMutationVars {
  directoryId: string;
  directoryInfo?: DirectoryInfo;
}

export const TreeContext = createContext<TreeContextProps | undefined>(undefined);

export const useTree = (): TreeContextProps => {
  const context = useContext(TreeContext);
  if (!context) {
    throw new Error('useTree must be used within a TreeProvider');
  }
  return context;
};

// Keep this export for backward compatibility with existing components
export const useTreeContext = useTree;

export const TreeProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const queryClient = useQueryClient();

  // Component state
  const [selectedDetails, setSelectedDetails] = useState<ComponentDetails | null>(null);
  const [selectedPath, setSelectedPath] = useState<string[]>([]);
  const [selectedNode, setSelectedNode] = useState<TreeNodeType | null>(null);
  const [isTreeVisible, setIsTreeVisible] = useState(true);
  const [currentDetailsPage, setCurrentDetailsPage] = useState<VersionDetailsCurrentPage>(
    VersionDetailsCurrentPage.ChangeHistory
  );

  // Prefetching configuration
  const prefetchEnabled = true; // Can be made configurable via props
  const prefetchDelay = 500; // Milliseconds to wait before prefetching

  // Track if we're loading details separately
  const [contentLoading, setContentLoading] = useState(false);

  // Use React Query hook for tree data instead of direct API call
  const {
    data: treeData,
    isLoading,
    error: queryError,
    refetch,
  } = useComponentTreeBranch({
    enabled: isAuthenticated,
  });

  // Create a mutation for updating the tree
  const updateTreeMutation = useMutation({
    mutationFn: async ({ directoryId, directoryInfo }: UpdateTreeMutationVars) => {
      try {
        // If we have directory info, use it to create the tree structure
        if (directoryInfo) {
          // Load only the first page - let infinite scrolling handle the rest
          const firstPageResult = await fetchTreeNodes(directoryId, 1, 50);
          const firstPageChildren = firstPageResult.nodes;

          // Create a tree with the directory as the root and only first page of children
          return [
            {
              id: directoryInfo.id,
              name: directoryInfo.name,
              description: directoryInfo.description || '',
              type: directoryInfo.type,
              parentId: '',
              componentType: directoryInfo.componentType || null,
              path: directoryInfo.path || [],
              children: firstPageChildren,
              isOpen: true, // Make sure the directory is open by default
              hasMoreChildren: firstPageResult.hasMorePages, // Indicate if more pages exist
              currentPage: 1, // Track current page for infinite scrolling
              totalCount: firstPageResult.totalCount, // Total count for reference
            },
          ];
        }

        // Otherwise use the createTree function directly
        return await createTree(directoryId, true);
      } catch (error) {
        logError('TreeContext', 'Error in mutation function:', error);
        throw error;
      }
    },
    onMutate: async ({ directoryId, directoryInfo }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['componentTreeBranch'] });

      // Snapshot the previous value
      const previousTreeData = queryClient.getQueryData(['componentTreeBranch']);

      // If we have directory info, create an optimistic update
      if (directoryInfo) {
        // Create an optimistic tree node while we fetch children
        const optimisticTree = [
          {
            id: directoryInfo.id,
            name: directoryInfo.name,
            description: directoryInfo.description || '',
            type: directoryInfo.type,
            parentId: '',
            componentType: directoryInfo.componentType || null,
            path: directoryInfo.path || [],
            children: [], // Empty children until real data arrives
            isOpen: true,
          },
        ];

        // Optimistically update to the new value
        queryClient.setQueryData(['componentTreeBranch'], optimisticTree);
      }

      // Return a context object with the previous tree data
      return { previousTreeData };
    },
    onError: (error, _variables, context) => {
      logError('TreeContext', 'Error updating tree with directory:', error);

      // If we have a context with previous tree data, roll back to it
      if (context?.previousTreeData) {
        queryClient.setQueryData(['componentTreeBranch'], context.previousTreeData);
      }
    },
    onSuccess: data => {
      // Update the cache with the new tree data
      logDebug('TreeContext', 'Successfully updated tree data');
      queryClient.setQueryData(['componentTreeBranch'], data);
    },
  });

  // Refetch data when authentication state changes
  useEffect(() => {
    if (isAuthenticated) {
      refetch().catch(error => {
        logError('TreeContext', 'Error fetching data:', error);
      });
    }
  }, [isAuthenticated, refetch]);

  // State to keep track of expanded nodes - using Set for better performance
  const [expandedNodeIds, setExpandedNodeIds] = useState<Set<string>>(new Set());

  // Check if a node is expanded
  const isNodeExpanded = useCallback(
    (nodeId: string): boolean => {
      return expandedNodeIds.has(nodeId);
    },
    [expandedNodeIds]
  );

  // Toggle node expansion state
  const toggleNodeExpansion = useCallback((nodeId: string): void => {
    setExpandedNodeIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // Expand a node
  const expandNode = useCallback((nodeId: string): void => {
    setExpandedNodeIds(prev => {
      const newSet = new Set(prev);
      newSet.add(nodeId);
      return newSet;
    });
  }, []);

  // Collapse a node
  const collapseNode = useCallback((nodeId: string): void => {
    setExpandedNodeIds(prev => {
      const newSet = new Set(prev);
      newSet.delete(nodeId);
      return newSet;
    });
  }, []);

  // When a node is expanded, fetch its children
  const handleNodeExpand = useCallback(
    (nodeId: string) => {
      // Add to expanded nodes set
      expandNode(nodeId);
    },
    [expandNode]
  );

  // Function to load more nodes when needed (e.g., when scrolling to the end of a large directory)
  const loadMoreNodes = useCallback(async (nodeId: string) => {
    // This function would be called when the user reaches the end of the visible nodes
    // The actual loading is handled by useDirectoryChildren with incrementalLoading=true
  }, []);

  // Memoized callback functions to prevent unnecessary re-renders
  const toggleTreeVisibility = useCallback(() => {
    setIsTreeVisible(prev => !prev);
  }, []);

  const handleComponentSelect = useCallback((details: ComponentDetails | null, path: string[]) => {
    if (details) {
      setContentLoading(true);
      // Simulate a microtask delay to prevent immediate content loading flag reset
      Promise.resolve().then(() => {
        setSelectedDetails(details);
        setSelectedPath(path);
        setContentLoading(false);
      });
    } else {
      setSelectedDetails(null);
      setSelectedPath(path);
      setContentLoading(false);
    }
  }, []);

  // Function to update the tree with a new root directory
  const updateTreeWithDirectory = useCallback(
    async (directoryId: string, directoryInfo?: DirectoryInfo) => {
      // Explicitly invalidate any existing queries before mutation
      await queryClient.invalidateQueries({ queryKey: ['componentTreeBranch'] });
      logDebug('TreeContext', 'Invalidated existing tree queries');

      // Execute the mutation and return a promise
      return new Promise<void>((resolve, reject) => {
        updateTreeMutation.mutate(
          { directoryId, directoryInfo },
          {
            onSuccess: data => {
              logDebug('TreeContext', 'Tree mutation completed successfully');

              // Update the cache with the data as-is, without forcing expansion
              if (data && Array.isArray(data)) {
                queryClient.setQueryData(['componentTreeBranch'], data);
              }

              resolve();
            },
            onError: error => {
              logError('TreeContext', 'Tree mutation failed:', error);
              reject(error);
            },
          }
        );
      });
    },
    [updateTreeMutation, queryClient]
  );

  // Create context value with all needed properties
  const contextValue = useMemo(
    () => ({
      data: treeData || [],
      loading: isLoading || updateTreeMutation.isPending,
      treeLoading: isLoading || updateTreeMutation.isPending,
      contentLoading,
      error: queryError ? String(queryError) : null,
      selectedDetails,
      setSelectedDetails,
      selectedPath,
      setSelectedPath,
      selectedNode,
      setSelectedNode,
      isTreeVisible,
      toggleTreeVisibility,
      handleComponentSelect,
      currentDetailsPage,
      setCurrentDetailsPage,
      updateTreeWithDirectory,
      // Add node expansion state management methods
      isNodeExpanded,
      toggleNodeExpansion,
      expandNode,
      collapseNode,
      // Add prefetching configuration
      prefetchEnabled,
      prefetchDelay,
    }),
    [
      treeData,
      isLoading,
      queryError,
      selectedDetails,
      selectedPath,
      selectedNode,
      isTreeVisible,
      toggleTreeVisibility,
      handleComponentSelect,
      currentDetailsPage,
      updateTreeWithDirectory,
      updateTreeMutation.isPending,
      contentLoading,
      isNodeExpanded,
      toggleNodeExpansion,
      expandNode,
      collapseNode,
      prefetchEnabled,
      prefetchDelay,
    ]
  );

  return <TreeContext.Provider value={contextValue}>{children}</TreeContext.Provider>;
};
