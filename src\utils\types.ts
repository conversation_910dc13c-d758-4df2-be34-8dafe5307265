// src/utils/types.ts
import CompareResult from './compareResult';
import { ExecutionConfiguration, ExecutionState, JobStatus } from './jobs';

export interface ComponentDetails {
  name: string;
  id: string;
  componentId?: string; // Additional identifier sometimes used
  objectId?: string; // Alternative identifier sometimes used
  componentType?: ComponentType | null;
  versions?: Version[];
  jobs?: Job[];
  localVersion?: Version | null;
  backupJobConfigured?: boolean;
  masterData?: MasterDataType[];
  data1?: string; // Customer
  data2?: string; // Plant
  data3?: string; // Country
  data4?: string; // City
  data5?: string; // Project
  data6?: string; // Workshop
  data7?: string; // Area
  data8?: string;
  data9?: string;
  data10?: string;
  directoryId?: string;
  userId?: string;
}

export interface ComponentType {
  id: string;
  name: string;
  icon: string;
}

export interface Version {
  componentId: string;
  index: number;
  version: number;
  versionIdentifier: string;
  timestamp: Date;
  username: string;
  changeReason: string;
  comment?: string;
  checksum?: string;
  componentName?: string;
  versionId?: string;
  status?: string;
  metadata?: Record<string, any>;
}

export interface VersionChange {
  path: string;
  previousContent: string;
  currentContent: string;
  fileType?: string;
}

export interface VersionComparisonResult {
  changes: VersionChange[];
  summary?: string;
}

// Updated TreeNode interface
export interface TreeNodeType {
  componentType: ComponentType | null;
  description: string;
  id: string;
  name: string;
  parentId: string;
  type: string;
  children?: TreeNodeType[];
  versionDetails?: Version[];
  displayPath?: string;
  path: string[];
  isOpen?: boolean;
}

// ... rest of your existing types ...

export interface Component {
  id: string;
  name: string;
  directory: string;
  parentId: string;
  type: string;
  userId: string;
  componentType: ComponentType;
  versions: Version[];
  localVersion: Version | null;
  backupJobConfigured: boolean;
  masterData: MasterDataType[];
}

export interface ComponentTypeType {
  id: string;
  name: string;
  icon: string;
}

export interface DirectoryNodeType {
  id: string;
  name: string;
  parentId: string;
  type: string;
  children: DirectoryNodeType[];
  components: ComponentNodeType[];
}

export interface ComponentNodeType {
  id: string;
  name: string;
  parentId: string;
  type: string;
  componentType: ComponentTypeType;
}

export interface Job {
  name: string;
  created: Date;
  id: string;
  componentId: string;
  executionState: ExecutionState;
  executionConfiguration: ExecutionConfiguration;
  isDeactivated: boolean;
  deactivatedComment: string;
  isRepeated: boolean;
  isScheduled: boolean;
  lastExecution?: Date;
  numberOfRepeats?: number;
  repeatDelay?: number;
  uploadType?: string;
  latestResult: CompareResults;
  results?: JobResult[];
  status?: JobStatus;
  whoToNotify?: string;
}

export interface JobResult {
  id: number;
  timestamp: Date;
  backup: string;
  location: string;
  version: number;
  result: CompareResults;
  eventLog: EventLogEntry[];
}

export interface CompareResults {
  versionVsBackup: CompareResult;
  backupVsBackup: CompareResult;
}

export interface EventLogEntry {
  timestamp: Date;
  text: string;
  type: EventLogType;
}

export enum EventLogType {
  information,
  error,
  warning,
}

export enum LockState {
  unlocked,
  locked,
  underDevelopment,
}

export interface MasterDataType {
  key: string;
  value: string;
}
