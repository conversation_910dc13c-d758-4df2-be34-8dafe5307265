
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for octoplant-web-application/src/authSettings.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">octoplant-web-application/src</a> authSettings.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/77</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/77</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import { Log, UserManagerSettings, WebStorageStateStore } from 'oidc-client-ts';<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
import { getConfig } from '@/config';
&nbsp;
// Get runtime configuration
<span class="cstat-no" title="statement not covered" >const config = getConfig();</span>
&nbsp;
// Set log level based on runtime configuration
<span class="cstat-no" title="statement not covered" >const isProduction = config.ENVIRONMENT === 'production';</span>
<span class="cstat-no" title="statement not covered" >const logLevel = config.LOG_LEVEL || 'ERROR';</span>
<span class="cstat-no" title="statement not covered" >Log.setLogger(console);</span>
&nbsp;
// Map string log level to oidc-client-ts log levels
<span class="cstat-no" title="statement not covered" >const getLogLevel = (level: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  switch (level.toUpperCase()) {</span>
<span class="cstat-no" title="statement not covered" >    case 'DEBUG':</span>
<span class="cstat-no" title="statement not covered" >      return Log.DEBUG;</span>
<span class="cstat-no" title="statement not covered" >    case 'INFO':</span>
<span class="cstat-no" title="statement not covered" >      return Log.INFO;</span>
<span class="cstat-no" title="statement not covered" >    case 'WARN':</span>
<span class="cstat-no" title="statement not covered" >      return Log.WARN;</span>
<span class="cstat-no" title="statement not covered" >    case 'ERROR':</span>
<span class="cstat-no" title="statement not covered" >      return Log.ERROR;</span>
<span class="cstat-no" title="statement not covered" >    default:</span>
<span class="cstat-no" title="statement not covered" >      return isProduction ? Log.ERROR : Log.WARN;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >Log.setLevel(getLogLevel(logLevel));</span>
&nbsp;
// Get hostname from runtime config or current browser location
<span class="cstat-no" title="statement not covered" >const getHostname = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  if (config.HOSTNAME) {</span>
<span class="cstat-no" title="statement not covered" >    return config.HOSTNAME;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
  // Extract hostname from current browser location as fallback
<span class="cstat-no" title="statement not covered" >  const currentHostname = window.location.hostname;</span>
<span class="cstat-no" title="statement not covered" >  if (currentHostname &amp;&amp; currentHostname !== 'localhost') {</span>
<span class="cstat-no" title="statement not covered" >    return currentHostname;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  throw new Error('HOSTNAME must be set in runtime configuration. Check your config.js file.');</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// API URL configuration from runtime config
<span class="cstat-no" title="statement not covered" >const API_BASE_URL = config.API_URL;</span>
<span class="cstat-no" title="statement not covered" >if (!API_BASE_URL.endsWith('/api/') &amp;&amp; !API_BASE_URL.endsWith('/api')) {</span>
<span class="cstat-no" title="statement not covered" >  throw new Error(</span>
<span class="cstat-no" title="statement not covered" >    'API_URL must end with /api/ (e.g., https://hostname:5256/api/). Please fix your config.js file.'</span>
<span class="cstat-no" title="statement not covered" >  );</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
// OAuth URL configuration
<span class="cstat-no" title="statement not covered" >const getBaseOAuthUrl = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const envUrl = config.OAUTH_URL;</span>
<span class="cstat-no" title="statement not covered" >  return envUrl.endsWith('/') ? envUrl : `${envUrl}/`;</span>
<span class="cstat-no" title="statement not covered" >};</span>
<span class="cstat-no" title="statement not covered" >const OAUTH_API_URL = `${getBaseOAuthUrl()}v1/oauth2/`;</span>
&nbsp;
// Frontend URL configuration
<span class="cstat-no" title="statement not covered" >const getFrontendUrl = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const hostname = getHostname();</span>
<span class="cstat-no" title="statement not covered" >  const port = config.PORT || window.location.port || '3000';</span>
<span class="cstat-no" title="statement not covered" >  const protocol = window.location.protocol || 'https:';</span>
<span class="cstat-no" title="statement not covered" >  return `${protocol}//${hostname}:${port}`;</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// OIDC UserManager settings
<span class="cstat-no" title="statement not covered" >export const settings: UserManagerSettings = {</span>
  // Core settings
<span class="cstat-no" title="statement not covered" >  authority: getBaseOAuthUrl(),</span>
<span class="cstat-no" title="statement not covered" >  metadata: {</span>
<span class="cstat-no" title="statement not covered" >    issuer: getBaseOAuthUrl(),</span>
<span class="cstat-no" title="statement not covered" >    authorization_endpoint: `${OAUTH_API_URL}authorize`,</span>
<span class="cstat-no" title="statement not covered" >    token_endpoint: `${OAUTH_API_URL}token`,</span>
<span class="cstat-no" title="statement not covered" >    jwks_uri: `${OAUTH_API_URL}jwks`,</span>
<span class="cstat-no" title="statement not covered" >    end_session_endpoint: `${OAUTH_API_URL}end_session`,</span>
<span class="cstat-no" title="statement not covered" >    response_types_supported: ['code'],</span>
<span class="cstat-no" title="statement not covered" >    subject_types_supported: ['public'],</span>
<span class="cstat-no" title="statement not covered" >    userinfo_endpoint: `${OAUTH_API_URL}userinfo`,</span>
<span class="cstat-no" title="statement not covered" >  },</span>
&nbsp;
  // Client configuration from runtime config
<span class="cstat-no" title="statement not covered" >  client_id: config.OAUTH_CLIENT_ID || 'octoplant-web-client',</span>
<span class="cstat-no" title="statement not covered" >  client_secret: 'b2135d46-c9d8-41f1-a4a2-558cd1dd8271',</span>
<span class="cstat-no" title="statement not covered" >  redirect_uri: config.OAUTH_REDIRECT_URI || `${getFrontendUrl()}/signin-callback`,</span>
<span class="cstat-no" title="statement not covered" >  post_logout_redirect_uri: getFrontendUrl(),</span>
<span class="cstat-no" title="statement not covered" >  response_type: 'code',</span>
<span class="cstat-no" title="statement not covered" >  scope: 'roles openid',</span>
&nbsp;
  // Feature configuration - enhanced with automatic renewal
<span class="cstat-no" title="statement not covered" >  loadUserInfo: true,</span>
<span class="cstat-no" title="statement not covered" >  userStore: new WebStorageStateStore({ store: window.localStorage }),</span>
<span class="cstat-no" title="statement not covered" >  monitorSession: true,</span>
&nbsp;
  // Enable both automatic silent renewal and custom token refresh for redundancy
<span class="cstat-no" title="statement not covered" >  automaticSilentRenew: true,</span>
<span class="cstat-no" title="statement not covered" >  silentRequestTimeoutInSeconds: 10,</span>
<span class="cstat-no" title="statement not covered" >  accessTokenExpiringNotificationTimeInSeconds: 60,</span>
<span class="cstat-no" title="statement not covered" >  checkSessionIntervalInSeconds: 10,</span>
<span class="cstat-no" title="statement not covered" >  silent_redirect_uri: `${getFrontendUrl()}/silent-callback`,</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  extraQueryParams: {</span>
<span class="cstat-no" title="statement not covered" >    audience: 'octoplant-web-client',</span>
<span class="cstat-no" title="statement not covered" >  },</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
// --- Developer Guidance ---
// IMPORTANT: VITE_API_URL must always end with /api/ (e.g., http://localhost:5256/api/)
// If you see an error about API URL, check your .env file and backend route prefix.
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-03T21:42:57.369Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    