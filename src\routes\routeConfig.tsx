import React, { lazy } from 'react';
import { RouteProps, Navigate } from 'react-router-dom';
import { ROUTES } from '@/constants/routes';
import { MainLayout } from '@/layouts';
import { TreeProvider } from '@/context/TreeContext';
import { withSuspense } from './withSuspense';
import AuthGateway from '@/components/Auth/AuthGateway';

// Import critical components directly to avoid loading screens
import TreeContainer from '@/components/Tree/TreeContainer';

// Only lazy load non-critical components
const Error = lazy(() => import('@/components/Error'));

// Route configuration
export const routeConfig: RouteProps[] = [
  {
    path: ROUTES.HOME,
    element: <Navigate to={ROUTES.TREE} />,
  },
  // Authentication routes - all handled by AuthGateway
  {
    path: ROUTES.LOGIN,
    element: <AuthGateway />,
  },
  {
    path: ROUTES.SIGNIN_CALLBACK,
    element: <AuthGateway />,
  },
  {
    path: ROUTES.SIGNOUT_CALLBACK,
    element: <AuthGateway />,
  },
  {
    path: ROUTES.SILENT_CALLBACK,
    element: <AuthGateway />,
  },
  // App routes - MainLayout is the top-level wrapper
  // Protection is handled by ProtectedRoute in App.tsx
  {
    path: ROUTES.TREE,
    element: (
      <MainLayout>
        <TreeProvider>
          <TreeContainer />
        </TreeProvider>
      </MainLayout>
    ),
  },
  {
    path: ROUTES.ERROR,
    element: withSuspense(() => (
      <MainLayout>
        <Error />
      </MainLayout>
    )),
  },
];
